# SIT Backend - Cursor Rules

This is a NestJS application with TypeORM, following enterprise-level patterns and best practices.

## Project Overview

- **Framework**: NestJS with TypeScript
- **Database**: PostgreSQL with TypeORM
- **Authentication**: JWT with role-based permissions
- **Documentation**: Swagger/OpenAPI
- **Internationalization**: i18n support (English/Vietnamese)
- **Testing**: Jest for unit and e2e tests
- **Code Quality**: ESLint + Prettier
- **Containerization**: Docker with docker-compose

## Quick Reference

### Key Files

- [main.ts](mdc:src/main.ts) - Application bootstrap
- [app.module.ts](mdc:src/app.module.ts) - Root module
- [package.json](mdc:package.json) - Dependencies and scripts
- [BaseEntity](mdc:src/common/base/entities/base.entity.ts) - Base entity for all models

### Common Scripts

- `npm run start:dev` - Development server
- `npm run migration:generate` - Generate migration
- `npm run migration:run` - Run migrations
- `npm run lint` - Lint code
- `npm run test` - Run tests

## Detailed Rules

For comprehensive guidelines, refer to the specific rule files:

- [Project Structure](mdc:.cursor/rules/project-structure.mdc) - Architecture and organization
- [Coding Standards](mdc:.cursor/rules/coding-standards.mdc) - Code patterns and conventions
- [Database Patterns](mdc:.cursor/rules/database-patterns.mdc) - TypeORM and database guidelines
- [Authentication & Security](mdc:.cursor/rules/authentication-security.mdc) - Auth and security patterns
- [API Best Practices](mdc:.cursor/rules/api-best-practices.mdc) - API design and implementation patterns
- [API Documentation](mdc:.cursor/rules/api-documentation.mdc) - Swagger and API guidelines
- [Validation & i18n](mdc:.cursor/rules/validation-i18n.mdc) - Validation and internationalization
- [Testing Guidelines](mdc:.cursor/rules/testing-guidelines.mdc) - Testing strategies and best practices
- [Development Workflow](mdc:.cursor/rules/development-workflow.mdc) - Development processes and scripts

## Core Principles

1. **Modular Architecture**: Each feature should be a separate module
2. **Type Safety**: Use TypeScript strictly with proper types
3. **Security First**: Implement proper authentication and authorization
4. **Internationalization**: Support multiple languages from the start
5. **Testing**: Write tests for all business logic
6. **Documentation**: Maintain comprehensive API documentation
7. **Code Quality**: Follow consistent coding standards
8. **Performance**: Optimize database queries and use proper indexing

## File Naming Conventions

- **Controllers**: `*.controller.ts`
- **Services**: `*.service.ts`
- **Entities**: `*.entity.ts`
- **DTOs**: `*.dto.ts`
- **Modules**: `*.module.ts`
- **Guards**: `*.guard.ts`
- **Filters**: `*.filter.ts`
- **Decorators**: `*.decorator.ts`

## Import Organization

1. External libraries (NestJS, TypeORM, etc.)
2. Internal modules (absolute imports with `@src/`)
3. Relative imports
4. Type imports

## Error Handling

- Use global exception filter for consistent error responses
- Throw appropriate HTTP exceptions
- Use i18n for error messages
- Log errors with proper context

## Database Guidelines

- All entities extend BaseEntity
- Use UUID v7 for primary keys
- Include proper indexes for performance
- Use migrations for schema changes
- Implement soft deletes where appropriate

## Security Checklist

- [ ] JWT authentication on protected routes
- [ ] Permission guards where needed
- [ ] Input validation with class-validator
- [ ] Password hashing with bcrypt
- [ ] CORS configuration
- [ ] Environment variable management
- [ ] Rate limiting for sensitive endpoints
