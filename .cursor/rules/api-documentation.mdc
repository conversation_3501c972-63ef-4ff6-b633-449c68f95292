# API Documentation Patterns

## Swagger Configuration

- Swagger setup in [main.ts](mdc:src/main.ts)
- Available at `/api/v1/docs` endpoint
- Configuration includes Bearer Auth and server prefix
- Custom CSS and branding support

## Controller Documentation

### Basic Controller Setup

```typescript
@ApiTags('Users')
@ApiBearerAuth()
@Controller('v1/users')
export class UsersController {
  // Controller methods
}
```

### Endpoint Documentation

```typescript
@Get()
@ApiOperation({
  summary: 'Get all users',
  description: 'Retrieve a paginated list of users with optional filtering'
})
@ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
@ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
@ApiResponse({
  status: 200,
  description: 'Users retrieved successfully',
  type: [UserResponseDto]
})
@ApiResponse({
  status: 401,
  description: 'Unauthorized - Invalid or missing token'
})
@ApiResponse({
  status: 403,
  description: 'Forbidden - Insufficient permissions'
})
async findAll(@Query() query: SearchUsersDto) {
  // Implementation
}
```

## DTO Documentation

### Request DTOs

```typescript
export class CreateUserDto {
  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
    type: String,
  })
  @IsEmail({}, { message: i18nValidationMessage('validation.IS_EMAIL') })
  email: string;

  @ApiProperty({
    description: 'User password (minimum 8 characters)',
    example: 'password123',
    minLength: 8,
  })
  @IsString()
  @MinLength(8, { message: i18nValidationMessage('validation.MIN_LENGTH') })
  password: string;

  @ApiProperty({
    description: 'User first name',
    example: 'John',
    required: false,
  })
  @IsOptional()
  @IsString()
  firstName?: string;
}
```

### Response DTOs

```typescript
export class UserResponseDto {
  @ApiProperty({
    description: 'Unique user identifier',
    example: '01890b2c-1234-5678-9abc-def123456789',
  })
  id: string;

  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'User first name',
    example: 'John',
  })
  firstName: string;

  @ApiProperty({
    description: 'User role',
    enum: ['customer', 'agency', 'affiliate', 'admin'],
    example: 'customer',
  })
  role: string;

  @ApiProperty({
    description: 'Account creation timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  createdAt: Date;
}
```

## Pagination Documentation

### Pagination DTO

```typescript
export class PaginationDto {
  @ApiProperty({
    description: 'Page number (starts from 1)',
    example: 1,
    default: 1,
    minimum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @Min(1)
  page?: number = 1;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
    default: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @Min(1)
  @Max(100)
  limit?: number = 10;
}
```

### Paginated Response

```typescript
export class PaginatedResponseDto<T> {
  @ApiProperty({
    description: 'Array of items'
  })
  data: T[];

  @ApiProperty({
    description: 'Pagination metadata'
  })
  meta: {
    @ApiProperty({ example: 1 })
    page: number;
    @ApiProperty({ example: 10 })
    limit: number;
    @ApiProperty({ example: 100 })
    total: number;
    @ApiProperty({ example: 10 })
    totalPages: number;
  };
}
```

## Error Response Documentation

### Global Error Response

```typescript
export class ErrorResponseDto {
  @ApiProperty({
    description: 'HTTP status code',
    example: 400,
  })
  statusCode: number;

  @ApiProperty({
    description: 'Error message',
    example: 'Validation failed',
  })
  message: string;

  @ApiProperty({
    description: 'Detailed validation errors',
    type: [ValidationErrorDto],
    required: false,
  })
  errors?: ValidationErrorDto[];

  @ApiProperty({
    description: 'Error timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  timestamp: string;

  @ApiProperty({
    description: 'Request path',
    example: '/api/v1/users',
  })
  path: string;
}
```

## Authentication Documentation

### JWT Authentication

```typescript
@Post('login')
@ApiOperation({
  summary: 'User login',
  description: 'Authenticate user and return JWT token'
})
@ApiBody({ type: LoginDto })
@ApiResponse({
  status: 200,
  description: 'Login successful',
  type: LoginResponseDto
})
@ApiResponse({
  status: 401,
  description: 'Invalid credentials'
})
async login(@Body() loginDto: LoginDto) {
  // Implementation
}
```

### Protected Routes

```typescript
@Get('profile')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@ApiOperation({
  summary: 'Get user profile',
  description: 'Retrieve current user profile information'
})
@ApiResponse({
  status: 200,
  description: 'Profile retrieved successfully',
  type: UserResponseDto
})
@ApiResponse({
  status: 401,
  description: 'Unauthorized - Invalid or missing token'
})
async getProfile(@Request() req) {
  // Implementation
}
```

## File Upload Documentation

### File Upload Endpoint

```typescript
@Post('upload')
@UseInterceptors(FileInterceptor('file'))
@ApiConsumes('multipart/form-data')
@ApiBody({
  description: 'File upload',
  type: FileUploadDto
})
@ApiResponse({
  status: 201,
  description: 'File uploaded successfully',
  type: FileUploadResponseDto
})
async uploadFile(@UploadedFile() file: Express.Multer.File) {
  // Implementation
}
```

## WebSocket Documentation

### WebSocket Gateway

```typescript
@WebSocketGateway({
  namespace: '/chat',
  cors: {
    origin: '*',
  },
})
@ApiTags('WebSocket')
export class ChatGateway {
  @SubscribeMessage('message')
  @ApiOperation({ summary: 'Send chat message' })
  handleMessage(@MessageBody() data: ChatMessageDto) {
    // Implementation
  }
}
```

## Documentation Best Practices

### General Guidelines

- Keep documentation up to date with code changes
- Use descriptive tags and summaries
- Include proper examples for complex endpoints
- Document rate limits and usage guidelines
- Provide clear error response documentation

### Versioning Strategy

- Use versioned routes: `v1/`, `v2/`, etc.
- Document version changes in Swagger
- Maintain backward compatibility when possible
- Use semantic versioning for API versions
- Document deprecated endpoints

### Security Documentation

- Document authentication requirements
- Include permission requirements
- Document rate limiting policies
- Provide security best practices
- Document data privacy policies

### Performance Documentation

- Document response time expectations
- Include caching strategies
- Document pagination limits
- Provide performance optimization tips
- Document monitoring endpoints

description: Comprehensive API documentation patterns and best practices
globs: ["src/**/*.controller.ts", "src/**/*.dto.ts"]
alwaysApply: true
