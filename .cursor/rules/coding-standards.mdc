# NestJS Coding Standards

## Entity Patterns

- All entities must extend [BaseEntity](mdc:src/common/base/entities/base.entity.ts)
- Use UUID v4 for primary keys (automatically generated)
- Include proper TypeORM decorators and indexes
- Follow naming convention: `EntityName` class, `entity_name` table
- Use `@DeleteDateColumn()` for soft deletes when appropriate
- Add indexes for frequently queried fields: `@Index('idx_field_name')`

## Controller Patterns

- Use versioned routes: `v1/`, `v2/`, etc.
- Apply `@ApiBearerAuth()` and `@ApiTags()` decorators for Swagger
- Use `CustomLogger` with context for logging
- Follow RESTful conventions for HTTP methods
- Use `@UseGuards()` for authentication and authorization
- Apply `@UseInterceptors()` for request/response transformation
- Use `@UsePipes()` for validation pipes

## DTO Patterns

- Use class-validator decorators with i18n validation messages
- Import validation messages: `import { i18nValidationMessage } from 'nestjs-i18n'`
- Use proper validation decorators: `@IsEmail()`, `@IsNotEmpty()`, `@IsOptional()`
- Follow naming convention: `CreateEntityDto`, `UpdateEntityDto`, etc.
- Use `@Transform()` for data transformation
- Use `@Expose()` and `@Exclude()` for serialization control

## Service Patterns

- Inject dependencies in constructor
- Use `CustomLogger` for logging with context
- Handle errors appropriately with proper exception types
- Use TypeORM repositories for database operations
- Implement proper transaction handling
- Use dependency injection for external services

## Module Organization

- Each module should have: `module.ts`, `controller.ts`, `service.ts`, `entities/`, `dto/`
- Use `@Module()` decorator with proper imports, controllers, providers, exports
- Follow dependency injection patterns
- Use `forwardRef()` for circular dependencies when necessary

## Import Conventions

- Use absolute imports with `@src/` prefix for internal modules
- Group imports: external libraries, internal modules, relative imports
- Use named imports for better tree-shaking
- Import types separately: `import type { TypeName } from 'module'`

## Error Handling

- Use global exception filter: [HttpFilterException](mdc:src/common/filters/http-exception.filter.ts)
- Throw appropriate HTTP exceptions: `BadRequestException`, `NotFoundException`, etc.
- Use i18n for error messages
- Log errors with proper context and stack traces
- Use custom exception classes for business logic errors

## Authentication & Authorization

- Use JWT authentication with [JwtAuthGuard](mdc:src/common/guards/jwt-auth.guard.ts)
- Apply permission guards where needed: [PermissionGuard](mdc:src/common/guards/permission.guard.ts)
- Use `@UseGuards()` decorator for route protection
- Use `@RequirePermissions()` decorator for fine-grained access control

## Testing Patterns

- Write unit tests for all services
- Use `@nestjs/testing` for mocking dependencies
- Test controllers with `supertest`
- Use test databases for integration tests
- Mock external services and APIs

## Performance Considerations

- Use database indexes for frequently queried fields
- Implement proper caching strategies
- Use pagination for large datasets
- Optimize database queries with proper joins
- Use connection pooling for database connections

## Code Quality

- Follow ESLint rules defined in [eslint.config.mjs](mdc:eslint.config.mjs)
- Use Prettier for code formatting
- Write meaningful commit messages
- Use TypeScript strict mode
- Avoid `any` types, use proper typing

description: Comprehensive coding standards for NestJS development
globs: ["src/**/*.ts"]
alwaysApply: true
