# Testing Guidelines & Best Practices

## Testing Strategy

### Testing Pyramid

- **Unit Tests** (70%) - Test individual functions and services
- **Integration Tests** (20%) - Test module interactions
- **E2E Tests** (10%) - Test complete user workflows

### Test Coverage Goals

- Minimum 80% code coverage
- 100% coverage for critical business logic
- Test all public methods and edge cases
- Mock external dependencies

## Unit Testing

### Service Testing

```typescript
describe('UsersService', () => {
  let service: UsersService;
  let repository: Repository<User>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: getRepositoryToken(User),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
            save: jest.fn(),
            create: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    repository = module.get<Repository<User>>(getRepositoryToken(User));
  });

  describe('findAll', () => {
    it('should return all users', async () => {
      const users = [{ id: '1', email: '<EMAIL>' }];
      jest.spyOn(repository, 'find').mockResolvedValue(users);

      const result = await service.findAll();

      expect(result).toEqual(users);
      expect(repository.find).toHaveBeenCalled();
    });

    it('should handle empty results', async () => {
      jest.spyOn(repository, 'find').mockResolvedValue([]);

      const result = await service.findAll();

      expect(result).toEqual([]);
    });

    it('should handle database errors', async () => {
      jest.spyOn(repository, 'find').mockRejectedValue(new Error('DB Error'));

      await expect(service.findAll()).rejects.toThrow('DB Error');
    });
  });
});
```

### Controller Testing

```typescript
describe('UsersController', () => {
  let controller: UsersController;
  let service: UsersService;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      controllers: [UsersController],
      providers: [
        {
          provide: UsersService,
          useValue: {
            findAll: jest.fn(),
            findOne: jest.fn(),
            create: jest.fn(),
            update: jest.fn(),
            remove: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<UsersController>(UsersController);
    service = module.get<UsersService>(UsersService);
  });

  describe('GET /users', () => {
    it('should return paginated users', async () => {
      const users = [{ id: '1', email: '<EMAIL>' }];
      const query = { page: 1, limit: 10 };
      jest.spyOn(service, 'findAll').mockResolvedValue({
        data: users,
        meta: { page: 1, limit: 10, total: 1, totalPages: 1 },
      });

      const result = await controller.findAll(query);

      expect(result.data).toEqual(users);
      expect(service.findAll).toHaveBeenCalledWith(query);
    });
  });
});
```

### DTO Testing

```typescript
describe('CreateUserDto', () => {
  it('should validate valid data', async () => {
    const dto = new CreateUserDto();
    dto.email = '<EMAIL>';
    dto.password = 'password123';
    dto.firstName = 'John';

    const errors = await validate(dto);
    expect(errors).toHaveLength(0);
  });

  it('should reject invalid email', async () => {
    const dto = new CreateUserDto();
    dto.email = 'invalid-email';
    dto.password = 'password123';

    const errors = await validate(dto);
    expect(errors).toHaveLength(1);
    expect(errors[0].constraints.isEmail).toBeDefined();
  });
});
```

## Integration Testing

### Module Testing

```typescript
describe('UsersModule', () => {
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [UsersModule],
    })
      .overrideProvider(getRepositoryToken(User))
      .useValue(mockRepository)
      .compile();
  });

  it('should be defined', () => {
    expect(module).toBeDefined();
  });

  it('should have UsersService', () => {
    const service = module.get<UsersService>(UsersService);
    expect(service).toBeDefined();
  });
});
```

### Database Integration Tests

```typescript
describe('UsersService Integration', () => {
  let app: INestApplication;
  let service: UsersService;
  let repository: Repository<User>;

  beforeAll(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(getRepositoryToken(User))
      .useValue(mockRepository)
      .compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    service = moduleFixture.get<UsersService>(UsersService);
    repository = moduleFixture.get<Repository<User>>(getRepositoryToken(User));
  });

  afterAll(async () => {
    await app.close();
  });

  it('should create and find user', async () => {
    const createUserDto = {
      email: '<EMAIL>',
      password: 'password123',
      firstName: 'John',
    };

    const createdUser = await service.create(createUserDto);
    expect(createdUser.email).toBe(createUserDto.email);

    const foundUser = await service.findOne(createdUser.id);
    expect(foundUser).toBeDefined();
    expect(foundUser.email).toBe(createUserDto.email);
  });
});
```

## E2E Testing

### API Endpoint Testing

```typescript
describe('Users (e2e)', () => {
  let app: INestApplication;
  let authToken: string;

  beforeAll(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Get auth token for protected routes
    const authResponse = await request(app.getHttpServer())
      .post('/api/v1/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'admin123',
      });

    authToken = authResponse.body.access_token;
  });

  afterAll(async () => {
    await app.close();
  });

  describe('GET /api/v1/users', () => {
    it('should return users list', () => {
      return request(app.getHttpServer())
        .get('/api/v1/users')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toBeDefined();
          expect(Array.isArray(res.body.data)).toBe(true);
        });
    });

    it('should require authentication', () => {
      return request(app.getHttpServer()).get('/api/v1/users').expect(401);
    });
  });

  describe('POST /api/v1/users', () => {
    it('should create new user', () => {
      const newUser = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'New',
        lastName: 'User',
      };

      return request(app.getHttpServer())
        .post('/api/v1/users')
        .set('Authorization', `Bearer ${authToken}`)
        .send(newUser)
        .expect(201)
        .expect((res) => {
          expect(res.body.email).toBe(newUser.email);
          expect(res.body.id).toBeDefined();
        });
    });

    it('should validate required fields', () => {
      const invalidUser = {
        email: 'invalid-email',
        password: '123', // too short
      };

      return request(app.getHttpServer())
        .post('/api/v1/users')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidUser)
        .expect(400)
        .expect((res) => {
          expect(res.body.errors).toBeDefined();
          expect(res.body.errors.length).toBeGreaterThan(0);
        });
    });
  });
});
```

## Mocking Strategies

### Repository Mocking

```typescript
const mockRepository = {
  find: jest.fn(),
  findOne: jest.fn(),
  save: jest.fn(),
  create: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  count: jest.fn(),
  createQueryBuilder: jest.fn(() => ({
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    take: jest.fn().mockReturnThis(),
    getManyAndCount: jest.fn(),
    getOne: jest.fn(),
  })),
};
```

### External Service Mocking

```typescript
const mockMailService = {
  sendEmail: jest.fn(),
  sendTemplate: jest.fn(),
};

const mockAuthService = {
  validateToken: jest.fn(),
  generateToken: jest.fn(),
};
```

## Test Utilities

### Test Database Setup

```typescript
export class TestDatabase {
  static async setup() {
    // Setup test database connection
    // Run migrations
    // Seed test data
  }

  static async teardown() {
    // Clean up test data
    // Close connections
  }
}
```

### Test Data Factories

```typescript
export class UserFactory {
  static create(overrides: Partial<User> = {}): User {
    return {
      id: 'test-id',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      password: 'hashedpassword',
      role: 'customer',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides,
    };
  }

  static createMany(count: number, overrides: Partial<User> = {}): User[] {
    return Array.from({ length: count }, (_, i) =>
      this.create({
        email: `test${i}@example.com`,
        ...overrides,
      }),
    );
  }
}
```

## Performance Testing

### Load Testing

```typescript
describe('Users API Performance', () => {
  it('should handle concurrent requests', async () => {
    const concurrentRequests = 10;
    const promises = Array.from({ length: concurrentRequests }, () =>
      request(app.getHttpServer())
        .get('/api/v1/users')
        .set('Authorization', `Bearer ${authToken}`),
    );

    const responses = await Promise.all(promises);

    responses.forEach((response) => {
      expect(response.status).toBe(200);
    });
  });
});
```

## Test Configuration

### Jest Configuration

```json
{
  "moduleFileExtensions": ["js", "json", "ts"],
  "rootDir": "src",
  "testRegex": ".*\\.spec\\.ts$",
  "transform": {
    "^.+\\.(t|j)s$": "ts-jest"
  },
  "collectCoverageFrom": ["**/*.(t|j)s", "!**/*.spec.ts", "!**/*.e2e-spec.ts"],
  "coverageDirectory": "../coverage",
  "testEnvironment": "node",
  "setupFilesAfterEnv": ["<rootDir>/test/setup.ts"]
}
```

## Best Practices

### Test Organization

- Group related tests using `describe` blocks
- Use descriptive test names
- Follow AAA pattern (Arrange, Act, Assert)
- Keep tests independent and isolated

### Test Data Management

- Use factories for test data creation
- Clean up test data after each test
- Use unique identifiers to avoid conflicts
- Mock external dependencies

### Assertions

- Use specific assertions
- Test both success and failure cases
- Verify side effects
- Test edge cases and error conditions

### Code Coverage

- Aim for high coverage but focus on quality
- Test critical business logic thoroughly
- Don't test implementation details
- Use coverage reports to identify gaps

description: Comprehensive testing guidelines and best practices for NestJS applications
globs: ["src/**/*.spec.ts", "test/**/*.ts"]
alwaysApply: true
