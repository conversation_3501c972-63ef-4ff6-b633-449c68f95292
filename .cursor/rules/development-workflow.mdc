# Development Workflow & Scripts

## Available Scripts

From [package.json](mdc:package.json):

### Database Operations

- `npm run migration:generate` - Generate new migration
- `npm run migration:run` - Run pending migrations
- `npm run migration:revert` - Revert last migration
- `npm run schema:drop` - Drop database schema
- `npm run seed:run` - Run database seeds
- `npm run seed:create` - Create new seed file

### Development

- `npm run start:dev` - Start development server with watch mode
- `npm run start:debug` - Start with debug mode
- `npm run build` - Build production bundle
- `npm run start:prod` - Start production server

### Code Quality

- `npm run lint` - Run ESLint with auto-fix
- `npm run format` - Format code with Prettier
- `npm run test` - Run unit tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:cov` - Run tests with coverage
- `npm run test:e2e` - Run end-to-end tests

## Docker Support

- [Dockerfile](mdc:Dockerfile) - Production container configuration
- [docker-compose.yml](mdc:docker-compose.yml) - Local development setup
- [start.sh](mdc:start.sh) - Startup script for container
- [.dockerignore](mdc:.dockerignore) - Docker ignore patterns

## Development Workflow

### 1. Setup Development Environment

```bash
# Install dependencies
npm install

# Setup environment variables
cp .env.example .env

# Start database with Docker
docker-compose up -d postgres redis

# Run migrations
npm run migration:run

# Start development server
npm run start:dev
```

### 2. Feature Development

1. Create feature branch from main
2. Implement feature following coding standards
3. Write tests for new functionality
4. Run linting and formatting
5. Create migration if database changes needed
6. Test locally with docker-compose

### 3. Testing Strategy

- **Unit Tests**: Test individual services and functions
- **Integration Tests**: Test module interactions
- **E2E Tests**: Test complete API endpoints
- **Coverage**: Aim for >80% code coverage
- **Test Database**: Use separate test database

### 4. Code Review Process

1. Self-review before creating PR
2. Run all tests and linting
3. Update documentation if needed
4. Create pull request with clear description
5. Address review feedback
6. Merge after approval

## Environment Configuration

- Configuration files in [src/common/configs/](mdc:src/common/configs/)
- Environment-specific settings
- Use `.env` files for local development
- Secure configuration management
- Validation with [validate-config.ts](mdc:src/common/utils/validate-config.ts)

## Logging

- Custom logger: [src/common/logging.ts](mdc:src/common/logging.ts)
- Winston-based logging with daily rotation
- Structured logging for better debugging
- Context-aware logging in services
- Log levels: error, warn, info, debug

## Health Checks

- Health module: [src/health/](mdc:src/health/)
- Database connectivity checks
- Service health monitoring
- Kubernetes readiness/liveness probes
- Custom health indicators

## Git Workflow

### Branch Naming

- `feature/feature-name` - New features
- `bugfix/bug-description` - Bug fixes
- `hotfix/urgent-fix` - Critical fixes
- `refactor/component-name` - Code refactoring

### Commit Messages

- Use conventional commits format
- Prefix: feat, fix, docs, style, refactor, test, chore
- Example: `feat(auth): add JWT refresh token functionality`

## Deployment Considerations

- Environment variable management
- Database migration strategy
- Health check endpoints
- Graceful shutdown handling
- Monitoring and alerting setup
- Docker container optimization
- Security scanning
- Performance monitoring

## Performance Optimization

- Database query optimization
- Caching strategies (Redis)
- Connection pooling
- Image optimization
- Bundle size optimization
- API response compression

## Security Checklist

- [ ] Environment variables secured
- [ ] Dependencies updated regularly
- [ ] Security headers configured
- [ ] Input validation implemented
- [ ] Authentication/authorization tested
- [ ] Rate limiting configured
- [ ] CORS properly configured
- [ ] HTTPS enforced in production

description: Comprehensive development workflow and best practices
globs: ["src/**/*", "package.json", "docker-compose.yml"]
alwaysApply: true
