# Validation & Internationalization Patterns

## DTO Validation

- Use class-validator decorators with i18n messages
- Import validation messages: `import { i18nValidationMessage } from 'nestjs-i18n'`
- Example from [create-user.dto.ts](mdc:src/modules/users/dto/create-user.dto.ts):
  ```typescript
  @IsEmail({}, { message: i18nValidationMessage('common.validation.isEmail') })
  @IsNotEmpty()
  email: string;
  ```

## Validation Decorators

- `@IsString()`, `@IsEmail()`, `@IsNotEmpty()`, `@IsOptional()`
- `@Length()`, `@Matches()` for regex validation
- `@IsEnum()` for enum validation
- `@IsNumber()`, `@IsBoolean()` for type validation

## Internationalization (i18n)

- Configuration in [app.module.ts](mdc:src/app.module.ts)
- Translation files in [src/common/i18n/](mdc:src/common/i18n/)
- Support for English (`en/`) and Vietnamese (`vi/`)
- Language resolution: query params, headers, Accept-Language

## Translation Structure

- Common translations: [src/common/i18n/en/common.json](mdc:src/common/i18n/en/common.json)
- User-specific translations: [src/common/i18n/en/user.json](mdc:src/common/i18n/en/user.json)
- Admin translations: [src/common/i18n/en/admin.json](mdc:src/common/i18n/en/admin.json)
- Email templates: [src/common/i18n/en/email.json](mdc:src/common/i18n/en/email.json)

## Validation Pipe Configuration

- Global validation pipe in [main.ts](mdc:src/main.ts)
- Custom exception factory for formatted errors
- Whitelist validation enabled
- Transform enabled for automatic type conversion

## Error Message Format

- Use dot notation for nested keys: `common.validation.isEmail`
- Provide fallback messages for missing translations
- Use consistent naming conventions across translation files
- Include context-specific validation messages

## Custom Validation

- Create custom validation decorators when needed
- Use `@ValidateIf()` for conditional validation
- Implement custom validation pipes for complex logic
- Use `@Transform()` for data transformation before validation

## Best Practices

- Use consistent validation message keys across the application
- Provide meaningful error messages in all supported languages
- Test validation with different language settings
- Use type-safe validation decorators
- Implement proper error handling for validation failures

description: Validation patterns and internationalization guidelines
globs: ["src/**/*.dto.ts", "src/common/i18n/**/*"]
alwaysApply: true
