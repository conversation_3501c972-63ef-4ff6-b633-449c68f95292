# Database Patterns & TypeORM Guidelines

## Entity Design

- Extend [BaseEntity](mdc:src/common/base/entities/base.entity.ts) for all entities
- Base entity provides: `id` (UUID v7), `createdAt`, `updatedAt`
- Use proper TypeORM decorators: `@Entity()`, `@Column()`, `@PrimaryGeneratedColumn()`
- Add indexes for frequently queried fields: `@Index('idx_field_name')`

## Migration Management

- Use TypeORM CLI commands from [package.json](mdc:package.json):
  - `npm run migration:generate` - Generate new migration
  - `npm run migration:run` - Run pending migrations
  - `npm run migration:revert` - Revert last migration
- Migration files stored in [src/common/database/migrations/](mdc:src/common/database/migrations/)
- Follow naming convention: `timestamp-Description.ts`

## Database Configuration

- Configuration in [src/common/configs/database.config.ts](mdc:src/common/configs/database.config.ts)
- TypeORM config service: [src/common/database/typeorm-config.service.ts](mdc:src/common/database/typeorm-config.service.ts)
- Data source configuration: [src/common/database/data-source.ts](mdc:src/common/database/data-source.ts)

## Relationship Patterns

- Use proper relationship decorators: `@OneToMany()`, `@ManyToOne()`, `@ManyToMany()`
- Include `@JoinColumn()` for foreign key relationships
- Use `@JoinTable()` for many-to-many relationships
- Consider cascade options for data integrity

## Query Patterns

- Use TypeORM repositories for database operations
- Implement proper error handling for database queries
- Use transactions for complex operations
- Consider query optimization with proper indexes

## Seeding

- Seed files in [src/common/database/seeds/](mdc:src/common/database/seeds/)
- Use `npm run seed:run` to execute seeds
- Create seeds with `npm run seed:create` (hygen)

## Best Practices

- Use soft deletes with `@DeleteDateColumn()` when appropriate
- Implement proper validation at entity level
- Use enums for fixed value fields
- Consider database constraints and foreign keys
- Use proper data types and lengths for columns
  description:
  globs:
  alwaysApply: false

---
