# SIT Backend Project Structure Guide

This is a NestJS application with TypeORM, following a modular architecture pattern with enterprise-level organization.

## Main Entry Points

- [main.ts](mdc:src/main.ts) - Application bootstrap with Swagger, validation, and global configurations
- [app.module.ts](mdc:src/app.module.ts) - Root module with global providers and middleware configuration

## Project Structure

### Core Modules (`src/modules/`)

- **Auth Module** (`src/modules/auth/`) - Authentication and authorization
  - Controllers: `auth.controller.ts`, `admin-auth.controller.ts`
  - Services: `auth.service.ts`
  - DTOs: `auth.dto.ts`, `dto/` directory
  - JWT strategy and payload interfaces

- **Users Module** (`src/modules/users/`) - User management with admin and website controllers
  - Controllers: `users.controller.ts`, `admin-users.controller.ts`, `admin-users-manager.controller.ts`
  - Services: `users.service.ts`
  - Entities: `entities/user.entity.ts`
  - DTOs: `dto/` directory with various user DTOs

- **Mail Module** (`src/modules/mail/`) - Email functionality with templates
  - Service: `mail.service.ts`
  - Templates: `mail-templates/` with i18n support
  - Assets: Fonts and images for email templates

- **Mailer Module** (`src/modules/mailer/`) - Email service implementation
  - Service: `mailer.service.ts`
  - Module configuration

### Common Utilities (`src/common/`)

- **Base Entities** (`src/common/base/entities/`) - Base entity with UUID v7 and timestamps
- **Configs** (`src/common/configs/`) - Environment-based configuration files
  - App, auth, database, AWS, mail, notification, and social login configs
- **Database** (`src/common/database/`) - TypeORM configuration, migrations, and seeds
  - Data source configuration
  - Migration files with timestamps
  - Seed files for initial data
- **Decorators** (`src/common/decorators/`) - Custom decorators like permissions
- **Filters** (`src/common/filters/`) - Exception filters
- **Guards** (`src/common/guards/`) - Authentication and permission guards
- **I18n** (`src/common/i18n/`) - Internationalization files (en/vi)
  - Admin, common, email, user, and voucher translations
- **Middlewares** (`src/common/middlewares/`) - Request context middleware
- **Permissions** (`src/common/permissions/`) - Permission definitions and groups
- **Utils** (`src/common/utils/`) - Utility functions and helpers
  - Date utilities, object utilities, payment utilities, phone utilities
  - Query utilities, upload service, validation helpers, XLSX helpers

### Health & WebSocket

- **Health Module** (`src/health/`) - Health check endpoints
  - Controller and module for health monitoring
- **WebSocket** (`src/websocket/`) - Real-time communication
  - Chat gateway for real-time features

## Configuration Files

- [package.json](mdc:package.json) - Dependencies and scripts
- [tsconfig.json](mdc:tsconfig.json) - TypeScript configuration
- [eslint.config.mjs](mdc:eslint.config.mjs) - ESLint rules
- [.prettierrc](mdc:.prettierrc) - Prettier configuration
- [nest-cli.json](mdc:nest-cli.json) - NestJS CLI configuration
- [Dockerfile](mdc:Dockerfile) - Container configuration
- [docker-compose.yml](mdc:docker-compose.yml) - Local development setup

## Key Patterns

- All entities extend [BaseEntity](mdc:src/common/base/entities/base.entity.ts)
- Controllers use versioned routes (e.g., `v1/users`)
- DTOs use class-validator with i18n validation messages
- Global JWT authentication guard applied to all routes
- Swagger documentation available at `/api/v1/docs`
- Internationalization support for English and Vietnamese
- Modular architecture with clear separation of concerns
- TypeORM for database operations with migrations
- Winston-based logging with daily rotation
- Docker containerization for deployment

## Module Template

Each new module should follow this structure:

```
module-name/
├── module-name.module.ts
├── module-name.controller.ts
├── module-name.service.ts
├── entities/
│   └── entity-name.entity.ts
├── dto/
│   ├── create-entity.dto.ts
│   ├── update-entity.dto.ts
│   └── search-entity.dto.ts
└── __tests__/
    ├── module-name.service.spec.ts
    └── module-name.controller.spec.ts
```

description: Comprehensive project structure and organization guidelines
globs: ["src/**/*"]
alwaysApply: true
