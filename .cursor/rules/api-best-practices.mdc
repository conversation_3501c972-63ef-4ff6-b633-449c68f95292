# API Best Practices & Patterns

## RESTful API Design

### URL Structure

- Use versioned routes: `/api/v1/resource`
- Use plural nouns for resources: `/api/v1/users`
- Use nested resources: `/api/v1/users/{id}/orders`
- Use query parameters for filtering: `/api/v1/users?role=admin&status=active`

### HTTP Methods

- `GET` - Retrieve resources (read-only)
- `POST` - Create new resources
- `PUT` - Update entire resource
- `PATCH` - Partial update of resource
- `DELETE` - Remove resource

### Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

## Controller Patterns

### Route Decorators

```typescript
@Controller('v1/users')
@ApiTags('Users')
@ApiBearerAuth()
export class UsersController {
  @Get()
  @ApiOperation({ summary: 'Get all users' })
  @ApiResponse({ status: 200, description: 'Users retrieved successfully' })
  async findAll(@Query() query: SearchUsersDto) {
    // Implementation
  }
}
```

### Request/Response Handling

- Use DTOs for request validation
- Use class-transformer for response serialization
- Implement proper error handling
- Use pagination for large datasets
- Include metadata in responses

### Pagination Pattern

```typescript
@Get()
async findAll(@Query() query: PaginationDto) {
  const { page = 1, limit = 10, ...filters } = query;
  const skip = (page - 1) * limit;

  const [data, total] = await this.usersService.findAll(skip, limit, filters);

  return {
    data,
    meta: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  };
}
```

## Validation Patterns

### DTO Validation

```typescript
export class CreateUserDto {
  @IsEmail({}, { message: i18nValidationMessage('validation.IS_EMAIL') })
  email: string;

  @IsString()
  @MinLength(8, { message: i18nValidationMessage('validation.MIN_LENGTH') })
  password: string;

  @IsOptional()
  @IsString()
  firstName?: string;
}
```

### Custom Validators

- Create custom validation decorators when needed
- Use validation pipes for complex validation logic
- Implement cross-field validation

## Error Handling

### Global Exception Filter

- Use [HttpExceptionFilter](mdc:src/common/filters/http-exception.filter.ts)
- Consistent error response format
- Proper HTTP status codes
- Internationalized error messages

### Error Response Format

```typescript
{
  "statusCode": 400,
  "message": "Validation failed",
  "errors": [
    {
      "field": "email",
      "message": "Email must be a valid email address"
    }
  ],
  "timestamp": "2024-01-15T10:30:00.000Z",
  "path": "/api/v1/users"
}
```

## Authentication & Authorization

### JWT Authentication

- Use [JwtAuthGuard](mdc:src/common/guards/jwt-auth.guard.ts) for protected routes
- Implement refresh token mechanism
- Proper token expiration handling

### Permission-Based Authorization

```typescript
@Get('admin/users')
@UseGuards(JwtAuthGuard, PermissionGuard)
@RequirePermissions('users:read:all')
async findAllAdmin() {
  // Admin-only implementation
}
```

## API Documentation

### Swagger/OpenAPI

- Use `@ApiOperation()` for endpoint descriptions
- Use `@ApiResponse()` for response documentation
- Use `@ApiProperty()` for DTO properties
- Use `@ApiBearerAuth()` for authentication

### Documentation Best Practices

- Clear and concise descriptions
- Include example requests/responses
- Document all possible error responses
- Use proper tags for organization

## Performance Optimization

### Database Queries

- Use proper indexes
- Implement query optimization
- Use pagination for large datasets
- Consider caching strategies

### Response Optimization

- Use `@Transform()` for data transformation
- Implement response compression
- Use proper HTTP caching headers
- Optimize JSON serialization

## Security Best Practices

### Input Validation

- Validate all inputs with DTOs
- Sanitize user inputs
- Use parameterized queries
- Implement rate limiting

### CORS Configuration

```typescript
app.enableCors({
  origin: configService.get('CORS_ORIGIN'),
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization'],
});
```

### Security Headers

- Implement security headers
- Use HTTPS in production
- Validate file uploads
- Implement proper session management

## Testing API Endpoints

### Unit Testing

```typescript
describe('UsersController', () => {
  let controller: UsersController;
  let service: UsersService;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      controllers: [UsersController],
      providers: [
        {
          provide: UsersService,
          useValue: {
            findAll: jest.fn(),
            findOne: jest.fn(),
            create: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<UsersController>(UsersController);
    service = module.get<UsersService>(UsersService);
  });

  it('should return users', async () => {
    const result = [{ id: '1', email: '<EMAIL>' }];
    jest.spyOn(service, 'findAll').mockResolvedValue(result);

    expect(await controller.findAll({})).toBe(result);
  });
});
```

### E2E Testing

```typescript
describe('Users (e2e)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  it('/api/v1/users (GET)', () => {
    return request(app.getHttpServer()).get('/api/v1/users').expect(200);
  });
});
```

## Monitoring & Logging

### Request Logging

- Log all API requests
- Include request ID for tracing
- Log response times
- Monitor error rates

### Health Checks

- Implement health check endpoints
- Monitor database connectivity
- Check external service dependencies
- Provide detailed health status

description: Comprehensive API design and implementation best practices
globs: ["src/**/*.controller.ts", "src/**/*.dto.ts"]
alwaysApply: true
