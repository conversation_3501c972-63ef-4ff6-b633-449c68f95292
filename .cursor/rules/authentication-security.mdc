# Authentication & Security Patterns

## JWT Authentication

- JWT strategy: [src/auth/jwt.strategy.ts](mdc:src/auth/jwt.strategy.ts)
- Auth module: [src/modules/auth/auth.module.ts](mdc:src/modules/auth/auth.module.ts)
- Auth service: [src/modules/auth/auth.service.ts](mdc:src/modules/auth/auth.service.ts)
- JWT payload interface: [src/modules/auth/jwt-payload.interface.ts](mdc:src/modules/auth/jwt-payload.interface.ts)

## Guards

- Global JWT guard: [src/common/guards/jwt-auth.guard.ts](mdc:src/common/guards/jwt-auth.guard.ts)
- Permission guard: [src/common/guards/permission.guard.ts](mdc:src/common/guards/permission.guard.ts)
- Apply guards using `@UseGuards()` decorator
- Global guard configured in [app.module.ts](mdc:src/app.module.ts)

## Permission System

- Permission decorator: [src/common/decorators/permission.decorator.ts](mdc:src/common/decorators/permission.decorator.ts)
- Permission definitions: [src/common/permissions/](mdc:src/common/permissions/)
- Use `@RequirePermissions()` decorator for route protection
- Permission groups: [src/common/permissions/permission_group.ts](mdc:src/common/permissions/permission_group.ts)

## Security Best Practices

- Use bcrypt for password hashing
- Implement proper password validation in DTOs
- Use environment variables for sensitive configuration
- Apply CORS configuration in [main.ts](mdc:src/main.ts)
- Use HTTPS in production
- Implement rate limiting where appropriate

## User Management

- User entity: [src/modules/users/entities/user.entity.ts](mdc:src/modules/users/entities/user.entity.ts)
- Admin controllers: [src/modules/users/admin-users.controller.ts](mdc:src/modules/users/admin-users.controller.ts)
- User types: `customer`, `agency`, `affiliate`
- Admin flag for administrative access

## Middleware

- Request context middleware: [src/common/middlewares/request-context.middleware.ts](mdc:src/common/middlewares/request-context.middleware.ts)
- Applied globally in [app.module.ts](mdc:src/app.module.ts)
- Provides user context and request tracking

## Error Handling

- Global exception filter: [src/common/filters/http-exception.filter.ts](mdc:src/common/filters/http-exception.filter.ts)
- Proper HTTP status codes for authentication errors
- Secure error messages (no sensitive data exposure)
  description:
  globs:
  alwaysApply: false

---
