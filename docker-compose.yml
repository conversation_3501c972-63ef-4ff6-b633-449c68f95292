version: '3.7'
services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "${PORT_EXPOSE}:${APP_PORT}"
    environment:
      - DATABASE_URL=**********************************/sit-db
      - NODE_ENV=development
    volumes:
     - .:/app
     - /app/node_modules
    depends_on:
      - db
    networks:
      - app-network
    container_name: sit-api
  db:
    image: postgres:16
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 123456
      POSTGRES_DB: sit-db
    ports:
      - "5434:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network
    container_name: sit-db
volumes:
  postgres_data:
networks:
  app-network:
    driver: bridge
