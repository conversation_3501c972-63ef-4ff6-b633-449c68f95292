DATABASE_TYPE='postgres'
DATABASE_HOST='localhost'
DATABASE_PORT='5434'
DATABASE_USERNAME='postgres'
DATABASE_PASSWORD='123456'
DATABASE_NAME='nestjs-base-db'
DATABASE_MAX_CONNECTIONS=20
DATABASE_QUERY_LOG_ENABLED=false

NODE_ENV=development
APP_PORT=3000
APP_NAME="Nestjs Base"
API_PREFIX=api
APP_FALLBACK_LANGUAGE=en
APP_HEADER_LANGUAGE=x-custom-lang
FRONTEND_DOMAIN=http://localhost:3001
ADMIN_DOMAIN=http://localhost:3002
BACKEND_DOMAIN=http://localhost:3000


LOG_LEVEL=debug
LOG_CONSOLE_ENABLED=true
LOG_FILE_ENABLED=true
LOG_DIR=C:/aipcon/logs
LOG_FILE_NAME_PATTERN=nestjs-base-app-%DATE%.log
LOG_FILE_MAX_SIZE=10m
LOG_FILE_RETENTION=7d

AUTH_JWT_SECRET=secret
AUTH_JWT_TOKEN_EXPIRES_IN=15m
AUTH_REFRESH_SECRET=secret_for_refresh
AUTH_REFRESH_TOKEN_EXPIRES_IN=3650d

SECRET_KEY="123456"




# Support "memory", "local", "s3"
FILE_DRIVER=local
ACCESS_KEY_ID=
SECRET_ACCESS_KEY=
AWS_S3_REGION=
AWS_DEFAULT_S3_BUCKET=
AWS_S3_ENDPOINT=
AWS_S3_MAX_FILE_SIZE=104857600 #100mb
PINATA_API_KEY=
PINATA_SECRET_API_KEY=
PINATA_JWT_KEY=

MAIL_HOST=maildev
MAIL_PORT=1025
MAIL_USER=wqeqweqwe # random value for maildev
MAIL_PASSWORD=eqweqeqwe # random
MAIL_IGNORE_TLS=true
MAIL_SECURE=false
MAIL_REQUIRE_TLS=false
MAIL_DEFAULT_EMAIL=<EMAIL>
MAIL_DEFAULT_NAME=Api
MAIL_DEFAULT_REPLY_TO=<EMAIL>

HOST=localhost:3000





REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=